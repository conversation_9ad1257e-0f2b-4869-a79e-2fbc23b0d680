<template>
  <div class="main">
    <SearchForm v-model:form="formArr" :page-type="PageType.PRODUCT_STOCK" @search="search" @setting="tableRef?.showTableSetting()" />
    <BaseTable ref="tableRef" :page-type="PageType.PRODUCT_STOCK" v-model:form="formArr" :get-list="getListFn" :is-checkbox="true" :footer-data="footerData" :show-footer="showFooter" :is-index="true">
      <template #main_image="{ row }">
        <div class="aa">
          <a-image
            v-if="row.images_view_url"
            :src="row.images_view_url"
            :class="lineHeightType == 1 ? '!w-40 !h-30' : lineHeightType == 2 ? '!w-50 !h-40' : '!w-60 !h-50'"
            :preview="{
              onVisibleChange: (previewVisible) => setPreviewVisible(row, previewVisible),
              src: row.bigImagesUrl,
            }"
          >
            <template #previewMask>
              <EyeOutlined />
            </template>
          </a-image>
        </div>
      </template>
      <template #supplier_product_number="{ row }">
        <copy-btn v-copy="row.supplier_product_number" v-if="row.supplier_product_number" />
        <span>{{ row.supplier_product_number }}</span>
      </template>
      <template #product_number="{ row }">
        <copy-btn v-copy="row.product_number" v-if="row.product_number" />
        <span>{{ row.product_number }}</span>
      </template>
      <template #operate="{ row }">
        <a-button type="text" @click="handleViewProduct(row.id, row)" :disabled="!btnPermission[840001]">查看</a-button>
      </template>
    </BaseTable>
    <ViewDrawer ref="viewRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { EyeOutlined } from '@ant-design/icons-vue'
import { PageType } from '@/common/enum'
import { GetList } from '@/servers/ProductLibrary'
import { usePermission } from '@/hook/usePermission'
import SearchForm from '@/components/SearchForm/index.vue'
import BaseTable from '@/components/BaseTable/index.vue'
import CopyBtn from '@/components/CopyBtn/index.vue'
import { GetCategoryOption, GetProductPreviewUrl } from '@/servers/ProductStock'
import ViewDrawer from './components/ViewDrawer.vue'

const { btnPermission } = usePermission()

// 搜索功能
const search = () => tableRef.value.search()

// 表格底部数据
const footerData = ref([{ seq: '合计', jst_inventory_num: 0 }]) // 实际库存jst_inventory_num
const showFooter = ref(true) // 是否显示底部合计行

const getListFn = async (params: any) => {
  if (Array.isArray(params.category_id_list) && params.category_id_list.length) {
    params.category_id_list = params.category_id_list.map((i) => i[i.length - 1])
  }
  params.is_get_total = true
  const res = await GetList(params)

  if (res?.data?.sum) {
    const totalActualStock = res.data.sum?.jst_inventory_num || 0
    footerData.value = [{ seq: '合计', jst_inventory_num: totalActualStock }]
    showFooter.value = true
  }

  return {
    data: { list: res.data.list, total: res.data.total },
  }
}
// 查看ref
const viewRef = ref()
// 表格行高
const lineHeightType = computed(() => tableRef.value.lineHeightType)

// 表格和表单引用
const tableRef = ref()
// 搜索表单配置
const formArr: any = ref([
  {
    label: '供应商商品编码',
    value: null,
    type: 'inputDlg',
    width: 240,
    key: 'supplier_product_number',
  },
  {
    label: '平台商品编码',
    value: null,
    type: 'inputDlg',
    width: 240,
    key: 'product_number',
  },
  {
    label: '商品名称',
    value: null,
    type: 'input',
    key: 'product_name',
  },
  {
    label: '商品类目',
    value: [],
    type: 'cascader',
    key: 'category_id_list',
    multiple: true,
    options: [],
    fieldNames: { label: 'label', value: 'value', children: 'children' },
  },
  {
    label: '更新时间',
    value: null,
    type: 'range-picker',
    options: [],
    key: 'modified_at',
    formKeys: ['modified_at_start', 'modified_at_end'],
    placeholder: ['更新开始日期', '更新结束日期'],
    valueFormat: 'YYYY-MM-DD',
  },
])

// 点击预览时才调接口
const setPreviewVisible = async (row: any, visible: boolean) => {
  if (visible) {
    try {
      const imgRes = await GetProductPreviewUrl({
        fileId: row.main_images_id,
        width: 999,
        height: 999,
      })
      row.bigImagesUrl = imgRes.data.view_url
    } catch (e) {
      row.bigImagesUrl = row.images_view_url
    }
  }
}

const getCategoryOption = async () => {
  const res = await GetCategoryOption()
  const formatTreeData = (categoryOptions) => {
    return categoryOptions.map((item) => ({
      label: item.name,
      value: item.id,
      children: item.children?.length ? formatTreeData(item.children) : undefined,
    }))
  }
  const treeData = formatTreeData(res.data)
  formArr.value.forEach((item) => {
    if (item.key === 'category_id_list') {
      item.options = treeData
    }
  })
}
// 查看
const handleViewProduct = (id: number, row: any) => {
  viewRef.value.showDrawer(id, row)
}

onMounted(() => {
  search()
  getCategoryOption()
})
</script>

<style lang="scss" scoped>
:deep(.ant-radio-group) {
  .ant-badge {
    &:not(:first-child) {
      .ant-radio-button-wrapper {
        border-end-start-radius: 0px;
        margin-left: -1px;
      }
    }

    &:last-child {
      .ant-radio-button-wrapper {
        border-end-end-radius: 4px;
      }
    }
  }

  .ant-badge-count {
    z-index: 2;
  }
}
</style>
