<template>
  <div class="main">
    <SearchForm v-model:form="formArr" :page-type="PageType.RETURN_ORDER_MANAGE" @search="search" @setting="tableRef?.showTableSetting()" />

    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.RETURN_ORDER_MANAGE" :get-list="getList" :merge-cells="mergeCells">
      <!-- 退库申请单编号 -->
      <template #number="{ row }">
        <div :style="{ lineHeight: autoLineHeight + 'px' }" class="copy-container">
          <div class="flex items-center">
            <!-- <span class="c-#999 mr-4">申请单号:</span> -->
            <span>{{ row.number || '--' }}</span>
            <CopyOutlined v-if="row.number" class="copy-icon" @click="copyToClipboard(row.number, '退库申请单编号')" />
          </div>
        </div>
      </template>

      <!-- 关联采购订单编号 -->
      <template #purchase_order_numbers="{ row }">
        <div :style="{ lineHeight: autoLineHeight + 'px' }" class="copy-container">
          <div v-if="row.purchase_order_numbers && row.purchase_order_numbers.length > 0">
            <div v-for="(orderNum, index) in row.purchase_order_numbers" :key="index" class="mb-1 copy-item">
              <!-- <span class="c-#999 mr-4">采购订单:</span> -->
              <span>{{ orderNum }}</span>
              <CopyOutlined class="copy-icon" @click="copyToClipboard(orderNum, '采购订单编号')" />
            </div>
          </div>
          <div v-else>
            <!-- <span class="c-#999 mr-4">采购订单:</span> -->
            <span>--</span>
          </div>
        </div>
      </template>

      <!-- 商品 -->
      <template #main_image="{ row }">
        <div class="flex items-center vxe-render-image">
          <BaseImage :src="row.image_url" />
          <div class="ml-8 c-#333" :style="{ lineHeight: autoLineHeight + 'px' }">
            <div>{{ row.sku_name || '--' }}</div>
            <div>
              <span class="c-#999 mr-4">类目:</span>
              <span>{{ row.all_category || '--' }}</span>
            </div>
          </div>
        </div>
      </template>

      <!-- 商品编码 -->
      <template #shop_code="{ row }">
        <div :style="{ lineHeight: autoLineHeight + 'px' }">
          <div>
            <span class="c-#999 mr-4">供应商:</span>
            <span>{{ row.srs_supplier_prod_code || '--' }}</span>
          </div>
          <div>
            <span class="c-#999 mr-4">平台:</span>
            <span>{{ row.srs_platform_prod_code || '--' }}</span>
          </div>
        </div>
      </template>

      <!-- 商品类目 -->
      <!-- <template #all_category="{ row }">
        <div :style="{ lineHeight: autoLineHeight + 'px' }">
          <div>
            <span>{{ row.all_category || '--' }}</span>
          </div>
        </div>
      </template> -->

      <!-- 供应商商品编码 -->
      <template #srs_supplier_prod_code="{ row }">
        <div :style="{ lineHeight: autoLineHeight + 'px' }" class="copy-container">
          <div>
            <!-- <span class="c-#999 mr-4">供应商编码:</span> -->
            <span>{{ row.srs_supplier_prod_code || '--' }}</span>
            <CopyOutlined v-if="row.srs_supplier_prod_code" class="copy-icon" @click="copyToClipboard(row.srs_supplier_prod_code, '供应商商品编码')" />
          </div>
        </div>
      </template>

      <!-- 平台商品编码 -->
      <template #srs_platform_prod_code="{ row }">
        <div :style="{ lineHeight: autoLineHeight + 'px' }" class="copy-container">
          <div>
            <!-- <span class="c-#999 mr-4">平台编码:</span> -->
            <span>{{ row.srs_platform_prod_code || '--' }}</span>
            <CopyOutlined v-if="row.srs_platform_prod_code" class="copy-icon" @click="copyToClipboard(row.srs_platform_prod_code, '平台商品编码')" />
          </div>
        </div>
      </template>

      <!-- 退货商品 -->
      <!-- <template #sku_name="{ row }">
        <div :style="{ lineHeight: autoLineHeight + 'px' }">
          <div>
            <span>{{ row.sku_name || '--' }}</span>
          </div>
        </div>
      </template> -->

      <!-- 退库数量 -->
      <template #return_quantity="{ row }">
        <div class="text-right">{{ row.return_quantity || 0 }}</div>
      </template>

      <!-- 退库金额 -->
      <template #return_amount="{ row }">
        <div class="text-right">¥{{ (row.return_amount || 0).toFixed(2) }}</div>
      </template>

      <!-- 其他费用 -->
      <template #other_fee_amount="{ row }">
        <div class="text-right">¥{{ (row.other_fee_amount || 0).toFixed(2) }}</div>
      </template>

      <!-- 退库总金额 -->
      <template #total_return_amount="{ row }">
        <div class="text-right">¥{{ (row.total_return_amount || 0).toFixed(2) }}</div>
      </template>

      <!-- 申请类型 -->
      <template #application_type_string="{ row }">
        <div>{{ row.application_type_string || '--' }}</div>
      </template>

      <!-- 退货原因 -->
      <template #return_reason_type_string="{ row }">
        <div>{{ row.return_reason_type_string || '--' }}</div>
      </template>

      <!-- 仓库名称 -->
      <template #warehouse_name="{ row }">
        <div>{{ row.warehouse_name || '--' }}</div>
      </template>

      <!-- 收货人 -->
      <template #consignee="{ row }">
        <div>{{ row.consignee || '--' }}</div>
      </template>

      <!-- 单据状态 -->
      <template #order_status_string="{ row }">
        <BaseBadge v-if="row.order_status_string" v-bind="formatOrderStatus(row.order_status)" />
        <span v-else>--</span>
      </template>

      <!-- 申请人 -->
      <template #creator_name="{ row }">
        <div>{{ row.creator_name || '--' }}</div>
      </template>

      <!-- 申请时间 -->
      <template #create_at="{ row }">
        <div>{{ row.create_at || '--' }}</div>
      </template>

      <!-- 操作 -->
      <template #operate="{ row }">
        <a-button type="link" size="small" @click="handleView(row)" :disabled="!btnPermission[930001]">查看</a-button>
      </template>
    </BaseTable>

    <!-- 详情抽屉 -->
    <ReturnOrderDetailDrawer ref="detailDrawerRef" />
  </div>
</template>

<script setup lang="ts">
import SearchForm from '@/components/SearchForm/index.vue'
import BaseTable from '@/components/BaseTable/index.vue'
import BaseBadge from '@/components/BaseBadge/index.vue'
import BaseImage from '@/components/BaseImage/index.vue'
import { PageType } from '@/common/enum'
import { GetReturnOrderList } from '@/servers/returnOrderManage'
import { message } from 'ant-design-vue'
import { CopyOutlined } from '@ant-design/icons-vue'

import { ref, useTemplateRef, computed, onMounted, nextTick } from 'vue'
import { VxeTablePropTypes } from 'vxe-table'
import { GetCommonOption } from '@/servers/Common'
import { usePermission } from '@/hook/usePermission'
import ReturnOrderDetailDrawer from './components/ReturnOrderDetailDrawer.vue'

const { btnPermission } = usePermission()

const tableRef = useTemplateRef<InstanceType<typeof BaseTable>>('tableRef')
const detailDrawerRef = useTemplateRef<InstanceType<typeof ReturnOrderDetailDrawer>>('detailDrawerRef')

// 合并单元格配置
const mergeCells = ref<VxeTablePropTypes.MergeCells>([])

// 自动行高计算
const autoLineHeight = computed(() => (tableRef?.value?.lineHeightType === 1 ? 16 : 24))

// 搜索表单配置
const formArr: any = ref([
  {
    label: '退货申请单编号',
    value: null,
    type: 'batch-input',
    key: 'number',
    width: 200,
    isShow: true,
  },
  {
    label: '采购订单编号',
    value: null,
    type: 'batch-input',
    key: 'purchase_order_numbers',
    width: 200,
    isShow: true,
  },
  {
    label: '供应商商品编码',
    value: null,
    type: 'batch-input',
    key: 'srs_supplier_prod_code',
    width: 200,
    isShow: true,
  },
  {
    label: '平台商品编码',
    value: null,
    type: 'batch-input',
    key: 'srs_platform_prod_code',
    width: 200,
    isShow: true,
  },
  {
    label: '商品名称',
    value: null,
    type: 'input',
    key: 'sku_name',
    isShow: true,
  },
  {
    label: '申请类型',
    value: null,
    type: 'select',
    key: 'application_type',
    isShow: true,
    options: [],
  },
  {
    label: '退货原因',
    value: null,
    type: 'select',
    key: 'return_reason_type',
    isShow: true,
    options: [],
  },
  {
    label: '单据状态',
    value: null,
    type: 'select',
    key: 'order_status',
    isShow: true,
    options: [],
  },
  {
    label: '申请人',
    value: null,
    type: 'input',
    key: 'creator_name',
    isShow: true,
  },
  {
    label: '申请时间',
    value: null,
    type: 'range-picker',
    key: 'create_time',
    formKeys: ['creator_start_time', 'creator_end_time'],
    placeholder: ['申请开始时间', '申请结束时间'],
    valueFormat: 'YYYY-MM-DD HH:mm:ss',
    isShow: true,
  },
])

const getList = async (params: any) => {
  // 处理批量输入的字段 - 将逗号分隔的字符串转换为数组
  if (params.number && typeof params.number === 'string') {
    params.number = params.number.split(',').filter((item: string) => item.trim())
  }
  if (params.purchase_order_numbers && typeof params.purchase_order_numbers === 'string') {
    params.purchase_order_numbers = params.purchase_order_numbers.split(',').filter((item: string) => item.trim())
  }
  if (params.srs_supplier_prod_code && typeof params.srs_supplier_prod_code === 'string') {
    params.srs_supplier_prod_code = params.srs_supplier_prod_code.split(',').filter((item: string) => item.trim())
  }
  if (params.srs_platform_prod_code && typeof params.srs_platform_prod_code === 'string') {
    params.srs_platform_prod_code = params.srs_platform_prod_code.split(',').filter((item: string) => item.trim())
  }

  const res = await GetReturnOrderList(params)
  const columns = tableRef.value?.tableRef?.getColumns() || []
  const mergeList: any[] = []
  mergeCells.value = []
  let findIndex = 0
  const list = res.data.list.flatMap((item: any, index: number) => {
    if (item.purchase_order_details?.length > 1) {
      // 为每个需要合并的列创建合并规则
      columns.forEach((col, colIndex) => {
        const mergeKeys = [
          'main_image', // 主图
          'sku_name', // 退货商品
          'shop_code', // 商品编码
          'all_category', // 商品类目
          'srs_supplier_prod_code', // 供应商商品编码
          'srs_platform_prod_code', // 平台商品编码
          'return_quantity', // 退库数量
          'return_amount', // 退库金额
        ]
        if (!mergeKeys.includes(col.field)) {
          mergeList.push({
            row: index + findIndex,
            col: colIndex,
            rowspan: item.purchase_order_details.length,
            colspan: 1,
          })
        }
      })
      findIndex += item.purchase_order_details.length - 1
    }
    // 如果没有商品明细，返回主对象
    if (!item.purchase_order_details?.length) {
      return {
        ...item,
        idx: index + 1 + (tableRef.value!.page - 1) * tableRef.value!.pageSize,
        pId: item.id,
      }
    }
    // 如果有商品明细，按商品明细展示
    return item.purchase_order_details.map((product: any, productIndex: number) => {
      product.pId = item.id
      return {
        ...item,
        ...product,
        idx: index + 1 + (tableRef.value!.page - 1) * tableRef.value!.pageSize,
      }
    })
  })

  nextTick(() => {
    mergeCells.value = mergeList
  })

  return {
    data: {
      list,
      total: res.data.total,
    },
  }
}

// 查询
const search = () => {
  tableRef.value?.search()
}

// 格式化订单状态
const formatOrderStatus = (status: number) => {
  const statusMap: { [key: number]: { type: 'success' | 'warning' | 'error' | 'info'; label: string } } = {
    1: { type: 'info', label: '进行中' }, // 蓝色
    2: { type: 'success', label: '已完成' }, // 绿色
  }
  return statusMap[status] || { type: 'info', label: '未知状态' }
}

const getCertificateType = async () => {
  // 获取选品状态、审核状态、提审人、选品人数据
  const dropdownRes = await GetCommonOption({ types: [25, 26, 27] }) // SRM退换单申请类型= 25    SRM退换单退货原因=26  SRM退换单单据状态=27

  const selectionapplication_type = dropdownRes.data?.data?.[25] || []
  const return_reason_type = dropdownRes.data?.data?.[26] || [] // 提审人选项
  const order_status = dropdownRes.data?.data?.[27] || [] // 提审人选项

  formArr.value.forEach((item: any) => {
    if (item.key === 'application_type') {
      // 申请类型下拉数据
      item.options = selectionapplication_type
    }
    if (item.key === 'return_reason_type') {
      // 换单退货原因下拉数据
      item.options = return_reason_type
    }
    if (item.key === 'order_status') {
      // 退换单单据状态拉数据
      item.options = order_status
    }
  })
}

// 查看详情
const handleView = (row: any) => {
  // 获取当前页的所有数据
  const currentPageData = tableRef.value?.tableData || []

  // 找到当前行在数据中的索引
  const currentIndex = currentPageData.findIndex((item: any) => item.pId === row.pId)

  if (currentIndex === -1) {
    message.error('无法找到当前数据')
    return
  }

  // 打开详情抽屉
  detailDrawerRef.value?.open(row, currentPageData, currentIndex)
}

// 复制到剪贴板（兼容处理）
const copyToClipboard = async (text: string, label: string) => {
  try {
    // 现代浏览器支持 clipboard API
    if (navigator.clipboard && typeof navigator.clipboard.writeText === 'function') {
      await navigator.clipboard.writeText(text)
      message.success(`${label}已复制到剪贴板`)
      return
    }

    // 降级方案：创建临时文本框执行复制
    const textarea = document.createElement('textarea')
    textarea.value = text
    // 隐藏文本框（避免影响页面布局）
    textarea.style.position = 'fixed'
    textarea.style.top = '-999px'
    textarea.style.left = '-999px'
    document.body.appendChild(textarea)
    // 选中并复制
    textarea.select()
    document.execCommand('copy')
    // 清理临时元素
    document.body.removeChild(textarea)
    message.success(`${label}已复制到剪贴板`)
  } catch (error) {
    console.error('复制失败:', error)
    message.error('复制失败，请手动复制')
  }
}

onMounted(async () => {
  getCertificateType()
})
</script>

<style scoped lang="scss">
.main {
  padding: 20px;
}

/* 复制容器样式 */
.copy-container {
  position: relative;

  .copy-icon {
    margin-left: 8px;
    font-size: 14px;
    color: #1890ff;
    cursor: pointer;
    transition: color 0.2s;
    opacity: 0;
    visibility: hidden;
    transition:
      opacity 0.2s,
      visibility 0.2s;

    &:hover {
      color: #40a9ff;
    }
  }

  &:hover .copy-icon {
    opacity: 1;
    visibility: visible;
  }
}

.copy-item {
  position: relative;

  .copy-icon {
    margin-left: 8px;
    font-size: 14px;
    color: #1890ff;
    cursor: pointer;
    transition: color 0.2s;
    opacity: 0;
    visibility: hidden;
    transition:
      opacity 0.2s,
      visibility 0.2s;

    &:hover {
      color: #40a9ff;
    }
  }

  &:hover .copy-icon {
    opacity: 1;
    visibility: visible;
  }
}

/* 表格样式 */
:deep(.table-box) {
  .vxe-table--header {
    height: 38px;
  }
}
</style>
